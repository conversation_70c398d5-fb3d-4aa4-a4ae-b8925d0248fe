<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\BeforePageIsResolvedEvent;

/**
 * Virtual Route Detection Listener
 * 
 * Listens to BeforePageIsResolvedEvent to detect virtual routes
 * and store the context for later processing.
 */
class VirtualRouteDetectionListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function __invoke(BeforePageIsResolvedEvent $event): void
    {
        $request = $event->getRequest();

        // Check if this is a virtual route request (set by our middleware)
        $virtualRouteMatch = $request->getAttribute('virtual_route');
        $templatePageUid = $request->getAttribute('virtual_route_template_page_uid');

        if ($virtualRouteMatch && $templatePageUid) {
            // Store virtual route context for later processing by other event listeners
            $this->virtualRouteService->setVirtualRoute($virtualRouteMatch);
        } else {
            // Clear any previous virtual route state for normal requests
            $this->virtualRouteService->clearVirtualRoute();
        }
    }
}
