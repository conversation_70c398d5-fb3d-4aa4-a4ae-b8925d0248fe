<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\EventListener;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use Bgs\FlightLandingPages\Service\VirtualRouteService;
use TYPO3\CMS\Frontend\Event\AfterCacheableContentIsGeneratedEvent;

/**
 * Virtual Content Processing Listener
 * 
 * Listens to AfterCacheableContentIsGeneratedEvent to process placeholders
 * in the generated content for virtual routes.
 */
class VirtualContentProcessingListener
{
    /**
     * @var VirtualRouteService
     */
    private $virtualRouteService;

    public function __construct(VirtualRouteService $virtualRouteService)
    {
        $this->virtualRouteService = $virtualRouteService;
    }

    public function __invoke(AfterCacheableContentIsGeneratedEvent $event): void
    {
        // Check if we're processing a virtual route
        if (!$this->virtualRouteService->isVirtualRoute()) {
            return;
        }

        $virtualRouteData = $this->virtualRouteService->getCurrentVirtualRoute();

        if (!$virtualRouteData) {
            return;
        }

        $request = $event->getRequest();
        $controller = $event->getController();

        // Get the current content from the controller
        $content = $controller->content ?? '';
        //echo(htmlentities($content));

        // Process placeholders in the content
        $processedContent = $this->virtualRouteService->processPlaceholdersInContent(
            $content,
            $virtualRouteData['flightRoute']
        );

        // Only update if content changed
        if ($processedContent !== $content) {
            // Update the controller's content directly
            $controller->content = $processedContent;
        }
    }
}
