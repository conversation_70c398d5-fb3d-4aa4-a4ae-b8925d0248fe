<?php
namespace Bgs\FlightLandingPages\Controller\Backend;

use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Backend\Utility\BackendUtility;
use TYPO3\CMS\Core\Authentication\BackendUserAuthentication;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Http\RedirectResponse;
use TYPO3\CMS\Core\Http\Response;
use TYPO3\CMS\Core\Imaging\IconFactory;

use TYPO3\CMS\Core\Localization\LanguageService;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\Page\PageRenderer;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use TYPO3\CMS\Fluid\View\StandaloneView;
use Bgs\FlightLandingPages\Service\UrlGenerationService;

/**
 * Backend controller for CSV export functionality
 */
class CsvExportController
{
    /**
     * @var ModuleTemplateFactory
     */
    protected $moduleTemplateFactory;

    /**
     * @var IconFactory
     */
    protected $iconFactory;

    /**
     * @var UriBuilder
     */
    protected $uriBuilder;

    /**
     * @var UrlGenerationService
     */
    protected $urlGenerationService;

    public function __construct(
        ModuleTemplateFactory $moduleTemplateFactory,
        IconFactory $iconFactory,
        UriBuilder $uriBuilder,
        UrlGenerationService $urlGenerationService
    ) {
        $this->moduleTemplateFactory = $moduleTemplateFactory;
        $this->iconFactory = $iconFactory;
        $this->uriBuilder = $uriBuilder;
        $this->urlGenerationService = $urlGenerationService;
    }
    /**
     * Export destination pairs as CSV
     */
    public function exportAction(ServerRequestInterface $request): ResponseInterface
    {
        // Get the landing page ID from request
        $queryParams = $request->getQueryParams();
        $landingPageId = (int)($queryParams['landingPageId'] ?? 0);

        if ($landingPageId === 0) {
            return $this->createErrorResponse('Invalid landing page ID');
        }

        // Check backend user permissions
        if (!$this->hasAccessToPage($landingPageId)) {
            return $this->createErrorResponse('Access denied');
        }

        // Get flight routes for this landing page
        $flightRoutes = $this->getFlightRoutes($landingPageId);

        if (empty($flightRoutes)) {
            return $this->createErrorResponse('No destination pairs found');
        }

        // Generate CSV content
        $csvContent = $this->generateCsvContent($flightRoutes, $landingPageId);

        // Get page title for filename
        $pageInfo = BackendUtility::getRecord('pages', $landingPageId);
        $pageTitle = $pageInfo['title'] ?? 'flight-routes';
        $filename = 'destination-pairs-' . preg_replace('/[^a-zA-Z0-9-_]/', '-', $pageTitle) . '-' . date('Y-m-d') . '.csv';

        // Create response with CSV content
        $response = new Response();
        $response->getBody()->write($csvContent);

        return $response
            ->withHeader('Content-Type', 'text/csv; charset=utf-8')
            ->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->withHeader('Cache-Control', 'no-cache, must-revalidate')
            ->withHeader('Pragma', 'no-cache')
            ->withHeader('Expires', '0');
    }

    /**
     * Get flight routes for a landing page
     */
    protected function getFlightRoutes(int $landingPageId): array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->orderBy('origin_code', 'ASC')
            ->addOrderBy('destination_code', 'ASC')
            ->executeQuery();

        return $result->fetchAllAssociative();
    }

    /**
     * Generate CSV content from flight routes
     */
    protected function generateCsvContent(array $flightRoutes, int $landingPageId): string
    {
        $csvLines = [];

        // Add CSV header
        $csvLines[] = $this->formatCsvRow([
            'Origin Code',
            'Origin Name',
            'Origin Type',
            'Destination Code',
            'Destination Name',
            'Destination Type',
            'Landing Page URL',
            'Is Active'
        ]);

        // Add data rows
        foreach ($flightRoutes as $route) {
            // Generate full URL for this route
            $fullUrl = '';
            if (!empty($route['route_slug'])) {
                // Route slug already contains the full path, so we just need to add the site base URL
                $fullUrl = $this->generateFullUrlFromSlug($landingPageId, $route['route_slug']);
            }

            $csvLines[] = $this->formatCsvRow([
                $route['origin_code'] ?? '',
                $route['origin_name'] ?? '',
                $route['origin_type'] ?? '',
                $route['destination_code'] ?? '',
                $route['destination_name'] ?? '',
                $route['destination_type'] ?? '',
                $fullUrl,
                $route['is_active'] ? '1' : '0'
            ]);
        }

        // Add BOM for proper UTF-8 handling in Excel
        return "\xEF\xBB\xBF" . implode("\n", $csvLines);
    }

    /**
     * Format a CSV row with proper escaping
     */
    protected function formatCsvRow(array $fields): string
    {
        $escapedFields = [];
        foreach ($fields as $field) {
            // Escape double quotes and wrap in quotes if necessary
            $field = str_replace('"', '""', (string)$field);
            if (strpos($field, ',') !== false || strpos($field, '"') !== false || strpos($field, "\n") !== false) {
                $field = '"' . $field . '"';
            }
            $escapedFields[] = $field;
        }
        return implode(',', $escapedFields);
    }

    /**
     * Check if current backend user has access to the page
     */
    protected function hasAccessToPage(int $pageId): bool
    {
        $backendUser = $this->getBackendUser();
        if (!$backendUser) {
            return false;
        }

        // Check if user has access to the page
        return $backendUser->doesUserHaveAccess(BackendUtility::getRecord('pages', $pageId), 1);
    }

    /**
     * Get current backend user
     */
    protected function getBackendUser(): ?BackendUserAuthentication
    {
        return $GLOBALS['BE_USER'] ?? null;
    }

    /**
     * Show import form
     */
    public function importFormAction(ServerRequestInterface $request): ResponseInterface
    {
        $queryParams = $request->getQueryParams();
        $landingPageId = (int)($queryParams['landingPageId'] ?? 0);
        $returnUrl = $queryParams['returnUrl'] ?? '';
        $errorMessage = $queryParams['error'] ?? '';

        if ($landingPageId === 0) {
            return $this->createErrorResponse('Invalid landing page ID');
        }

        // Check backend user permissions
        if (!$this->hasAccessToPage($landingPageId)) {
            return $this->createErrorResponse('Access denied');
        }

        // Generate import URL
        $importUrl = $this->generateImportUrl();

        // Create module template
        $moduleTemplate = $this->moduleTemplateFactory->create($request);

        // Set page title
        $pageInfo = BackendUtility::getRecord('pages', $landingPageId);
        $pageTitle = $pageInfo['title'] ?? 'Flight Landing Page';
        $moduleTemplate->setTitle(
            $this->getLanguageService()->sL('LLL:EXT:flight_landing_pages/Resources/Private/Language/locallang_db.xlf:pages.import.title'),
            $pageTitle
        );

        // Add close button to doc header
        $this->addDocHeaderCloseButton($moduleTemplate, $returnUrl);

        // Assign variables to template
        $moduleTemplate->assignMultiple([
            'landingPageId' => $landingPageId,
            'returnUrl' => $returnUrl,
            'importUrl' => $importUrl,
            'errorMessage' => $errorMessage,
        ]);

        return $moduleTemplate->renderResponse('Backend/CsvImport');
    }

    /**
     * Process CSV import
     */
    public function importAction(ServerRequestInterface $request): ResponseInterface
    {
        $parsedBody = $request->getParsedBody();
        $landingPageId = (int)($parsedBody['landingPageId'] ?? 0);
        $returnUrl = $parsedBody['returnUrl'] ?? '';

        if ($landingPageId === 0) {
            return $this->createErrorResponse('Invalid landing page ID');
        }

        // Check backend user permissions
        if (!$this->hasAccessToPage($landingPageId)) {
            return $this->createErrorResponse('Access denied');
        }

        try {
            // Process uploaded file
            $uploadedFiles = $request->getUploadedFiles();
            $csvFile = $uploadedFiles['csvFile'] ?? null;

            if (!$csvFile || $csvFile->getError() !== UPLOAD_ERR_OK) {
                throw new \Exception('No valid CSV file uploaded');
            }

            // Parse and import CSV
            $importedCount = $this->processCsvImport($csvFile, $landingPageId);

            // Redirect back with success message
            return $this->redirectWithMessage($returnUrl, 'success', $importedCount);

        } catch (\Exception $e) {
            // Redirect back to import form with error message
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
            $importFormUrl = (string)$uriBuilder->buildUriFromRoute('flight_landing_pages_csv_import_form', [
                'landingPageId' => $landingPageId,
                'returnUrl' => $returnUrl,
                'error' => $e->getMessage()
            ]);

            $response = new Response();
            return $response
                ->withStatus(302)
                ->withHeader('Location', $importFormUrl);
        }
    }

    /**
     * Generate import URL
     */
    protected function generateImportUrl(): string
    {
        try {
            $uriBuilder = GeneralUtility::makeInstance(UriBuilder::class);
            return (string)$uriBuilder->buildUriFromRoute('flight_landing_pages_csv_import');
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Process CSV import from uploaded file
     */
    protected function processCsvImport($csvFile, int $landingPageId): int
    {
        $csvContent = $csvFile->getStream()->getContents();

        // Remove BOM if present
        $csvContent = preg_replace('/^\xEF\xBB\xBF/', '', $csvContent);

        // Parse CSV
        $lines = str_getcsv($csvContent, "\n");
        if (empty($lines)) {
            throw new \Exception('CSV file is empty');
        }

        // Get header row
        $header = str_getcsv(array_shift($lines));
        $expectedColumns = [
            'Origin Code', 'Origin Name', 'Origin Type',
            'Destination Code', 'Destination Name', 'Destination Type'
        ];

        // Note: We ignore 'Landing Page URL' and 'Route Slug' columns completely and generate slugs from codes

        // Validate header
        $headerMap = [];
        foreach ($expectedColumns as $expectedCol) {
            $index = array_search($expectedCol, $header);
            if ($index === false) {
                throw new \Exception("Missing required column: {$expectedCol}");
            }
            $headerMap[$expectedCol] = $index;
        }

        // Check for optional "Is Active" column
        $isActiveColumnIndex = array_search('Is Active', $header);
        if ($isActiveColumnIndex !== false) {
            $headerMap['Is Active'] = $isActiveColumnIndex;
        }

        $connection = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getConnectionForTable('tx_flightlandingpages_domain_model_flightroute');

        // First pass: validate CSV data and check for internal duplicates
        $csvRoutes = [];
        $seenRoutes = [];
        $rowNumber = 1; // Start from 1 (header is row 0)

        foreach ($lines as $line) {
            $rowNumber++;
            $line = trim($line);
            if (empty($line)) {
                continue;
            }

            $data = str_getcsv($line);
            if (count($data) < count($expectedColumns)) {
                continue; // Skip incomplete rows
            }

            // Extract data using header mapping
            $originCode = trim($data[$headerMap['Origin Code']]);
            $destinationCode = trim($data[$headerMap['Destination Code']]);

            // Generate route slug from codes (ignore any URL/slug columns)
            $routeSlug = $this->generateRouteSlug($originCode, $destinationCode, $landingPageId);

            // Handle optional "Is Active" column
            $isActiveValue = '';
            if (isset($headerMap['Is Active']) && isset($data[$headerMap['Is Active']])) {
                $isActiveValue = $data[$headerMap['Is Active']];
            }

            $routeData = [
                'pid' => $landingPageId,
                'origin_code' => $originCode,
                'origin_name' => trim($data[$headerMap['Origin Name']]),
                'origin_type' => trim($data[$headerMap['Origin Type']]),
                'destination_code' => $destinationCode,
                'destination_name' => trim($data[$headerMap['Destination Name']]),
                'destination_type' => trim($data[$headerMap['Destination Type']]),
                'route_slug' => $routeSlug,
                'is_active' => $this->parseBoolean($isActiveValue),
            ];

            // Validate required fields
            if (empty($routeData['origin_code']) || empty($routeData['destination_code'])) {
                continue; // Skip rows with missing required data
            }

            // Create a unique key for duplicate detection
            $duplicateKey = sprintf(
                '%s|%s|%s|%s',
                $routeData['origin_type'],
                $routeData['origin_code'],
                $routeData['destination_type'],
                $routeData['destination_code']
            );

            // Check for duplicates within the CSV
            if (isset($seenRoutes[$duplicateKey])) {
                throw new \Exception(sprintf(
                    'Duplicate route found in CSV at row %d: Origin Type "%s", Origin Code "%s", Destination Type "%s", Destination Code "%s". First occurrence was at row %d.',
                    $rowNumber,
                    $routeData['origin_type'],
                    $routeData['origin_code'],
                    $routeData['destination_type'],
                    $routeData['destination_code'],
                    $seenRoutes[$duplicateKey]
                ));
            }

            // Mark this route as seen
            $seenRoutes[$duplicateKey] = $rowNumber;
            $csvRoutes[] = $routeData;
        }

        // Second pass: import the validated data
        $importedCount = 0;
        $currentTime = time();

        foreach ($csvRoutes as $routeData) {
            // Add timestamps
            $routeData['crdate'] = $currentTime;
            $routeData['tstamp'] = $currentTime;

            // Check if route already exists in database
            $existingRoute = $this->findExistingRoute(
                $landingPageId,
                $routeData['origin_code'],
                $routeData['destination_code']
            );

            if ($existingRoute) {
                // Update existing route
                $connection->update(
                    'tx_flightlandingpages_domain_model_flightroute',
                    array_merge($routeData, ['tstamp' => $currentTime]),
                    ['uid' => $existingRoute['uid']]
                );
            } else {
                // Insert new route
                $connection->insert('tx_flightlandingpages_domain_model_flightroute', $routeData);
            }

            $importedCount++;
        }

        return $importedCount;
    }

    /**
     * Generate route slug from origin and destination codes
     * Includes parent page path to match the expected format
     */
    protected function generateRouteSlug(string $originCode, string $destinationCode, int $landingPageId): string
    {
        if (empty($originCode) || empty($destinationCode)) {
            return '';
        }

        // Generate base slug from codes (lowercase with dash separator)
        $baseSlug = strtolower(trim($originCode)) . '-' . strtolower(trim($destinationCode));

        // Get parent page path
        $parentPagePath = $this->getParentPagePath($landingPageId);

        // Combine parent page path with base slug
        if (!empty($parentPagePath)) {
            $parentPagePath = ltrim($parentPagePath, '/');
            return $parentPagePath . '/' . $baseSlug;
        }

        return $baseSlug;
    }

    /**
     * Get the full path of the parent page including all parent pages
     */
    protected function getParentPagePath(int $pid): string
    {
        if ($pid <= 0) {
            return '';
        }

        try {
            $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
                ->getQueryBuilderForTable('pages');

            $page = $queryBuilder
                ->select('uid', 'pid', 'slug', 'title')
                ->from('pages')
                ->where(
                    $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($pid, \PDO::PARAM_INT)),
                    $queryBuilder->expr()->eq('deleted', 0),
                    $queryBuilder->expr()->eq('hidden', 0)
                )
                ->executeQuery()
                ->fetchAssociative();

            if (!$page) {
                return '';
            }

            // Use the page's slug if available
            if (!empty($page['slug'])) {
                return $page['slug'];
            }

            // Fallback: build path from title if no slug
            return '/' . $this->sanitizeSlug($page['title']);

        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Sanitize a string for use in a slug
     */
    protected function sanitizeSlug(string $input): string
    {
        // Convert to lowercase and replace spaces/special chars with dashes
        $slug = strtolower(trim($input));
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        return trim($slug, '-');
    }

    /**
     * Parse boolean value from CSV
     * Treats empty strings or missing values as active (1) by default
     * Only sets inactive (0) when explicitly specified
     */
    protected function parseBoolean($value): int
    {
        $value = strtolower(trim($value));

        // If empty or missing, default to active (1)
        if (empty($value)) {
            return 1;
        }

        // Only set inactive (0) for explicit inactive values
        return in_array($value, ['0', 'no', 'false', 'inactive', 'disabled']) ? 0 : 1;
    }

    /**
     * Generate full URL from route slug (which already contains the full path)
     */
    protected function generateFullUrlFromSlug(int $landingPageId, string $routeSlug): string
    {
        try {
            // Get site base URL
            $site = GeneralUtility::makeInstance(\TYPO3\CMS\Core\Site\SiteFinder::class)
                ->getSiteByPageId($landingPageId);
            $baseUrl = (string)$site->getBase();

            // Ensure base URL ends with /
            $baseUrl = rtrim($baseUrl, '/') . '/';

            // Route slug already contains the full path, just combine with base URL
            return $baseUrl . ltrim($routeSlug, '/');
        } catch (\Exception $e) {
            return '';
        }
    }

    /**
     * Find existing route by origin and destination codes
     */
    protected function findExistingRoute(int $landingPageId, string $originCode, string $destinationCode): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('uid')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageId, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('origin_code', $queryBuilder->createNamedParameter($originCode)),
                $queryBuilder->expr()->eq('destination_code', $queryBuilder->createNamedParameter($destinationCode)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT))
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Redirect with flash message
     */
    protected function redirectWithMessage(string $returnUrl, string $type, int $count = 0, string $error = ''): ResponseInterface
    {
        // For now, just redirect back - flash messages would require session handling
        $response = new Response();
        return $response
            ->withStatus(302)
            ->withHeader('Location', $returnUrl);
    }

    /**
     * Create error response
     */
    protected function createErrorResponse(string $message): ResponseInterface
    {
        $response = new Response();
        $response->getBody()->write($message);
        return $response->withStatus(400);
    }

    /**
     * Add close button to doc header
     */
    protected function addDocHeaderCloseButton($moduleTemplate, string $returnUrl): void
    {
        $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();
        $closeButton = $buttonBar->makeLinkButton()
            ->setTitle($this->getLanguageService()->sL('LLL:EXT:core/Resources/Private/Language/locallang_common.xlf:close'))
            ->setIcon($this->iconFactory->getIcon('actions-close', 'small'))
            ->setShowLabelText(true)
            ->setHref($returnUrl);
        $buttonBar->addButton($closeButton, ButtonBar::BUTTON_POSITION_LEFT);
    }

    /**
     * Get language service
     */
    protected function getLanguageService(): LanguageService
    {
        return $GLOBALS['LANG'] ?? GeneralUtility::makeInstance(LanguageService::class);
    }
}
