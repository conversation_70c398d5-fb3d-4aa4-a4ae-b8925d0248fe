<?php

declare(strict_types=1);

namespace Bgs\FlightLandingPages\Service;

use Bgs\FlightLandingPages\Domain\Model\VirtualRouteContext;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Site\Entity\Site;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Virtual Route Service
 * 
 * Manages virtual route state and provides utilities for virtual route detection
 * and page data manipulation.
 */
class VirtualRouteService
{
    private ?array $currentVirtualRoute = null;

    /**
     * Set the current virtual route data
     */
    public function setVirtualRoute(array $routeData): void
    {
        $this->currentVirtualRoute = $routeData;
    }

    /**
     * Get the current virtual route data
     */
    public function getCurrentVirtualRoute(): ?array
    {
        return $this->currentVirtualRoute;
    }

    /**
     * Clear the current virtual route
     */
    public function clearVirtualRoute(): void
    {
        $this->currentVirtualRoute = null;
    }

    /**
     * Check if we're currently processing a virtual route
     */
    public function isVirtualRoute(): bool
    {
        return $this->currentVirtualRoute !== null;
    }

    /**
     * Detect if a path matches a virtual route pattern
     */
    public function detectVirtualRoute(string $path, Site $site): ?array
    {
        $pathParts = explode('/', trim($path, '/'));

        if (count($pathParts) < 2) {
            return null;
        }

        // Get the last part which should be the route slug (e.g., "ist-var")
        $routeSlug = end($pathParts);

        // Remove the route slug from path to get the landing page path
        array_pop($pathParts);
        $landingPagePath = implode('/', $pathParts);

        // Ensure path starts with /
        $searchPath = '/' . ltrim($landingPagePath, '/');
        $searchPath = str_replace($site->getBase()->getPath(), '', $searchPath);
        $searchPathLP = ltrim($searchPath, '/');

        // Find matching landing page and flight route
        $landingPageData = $this->findLandingPageByPath($searchPath, $site);

        if (!$landingPageData) {
            return null;
        }

        // Check if there's a matching flight route
        $flightRoute = $this->findFlightRoute("{$searchPathLP}/{$routeSlug}", $landingPageData['uid']);

        if (!$flightRoute) {
            return null;
        }

        return [
            'landingPage' => $landingPageData,
            'flightRoute' => $flightRoute,
            'routeSlug' => $routeSlug,
            'originalPath' => $path,
            'templatePageUid' => $landingPageData['tx_flightlandingpages_template_page']
        ];
    }

    /**
     * Create virtual page data by merging landing page and template page
     */
    public function createVirtualPageData(array $landingPage, array $templatePage, array $flightRoute, string $originalPath): array
    {
        // Start with the landing page as base
        $virtualPage = $landingPage;

        // Override with template page content-related fields
        $virtualPage['title'] = $templatePage['title'] ?? $landingPage['title'];
        $virtualPage['subtitle'] = $templatePage['subtitle'] ?? $landingPage['subtitle'];
        $virtualPage['description'] = $templatePage['description'] ?? $landingPage['description'];
        $virtualPage['keywords'] = $templatePage['keywords'] ?? $landingPage['keywords'];
        $virtualPage['seo_title'] = $templatePage['seo_title'] ?? $landingPage['seo_title'];

        // Process placeholders in the virtual page data
        $virtualPage = $this->processPlaceholdersInPageData($virtualPage, $flightRoute);

        // Set virtual page properties
        $virtualPage['slug'] = '/' . ltrim($originalPath, '/');
        $virtualPage['is_virtual'] = true;
        $virtualPage['tx_flightlandingpages_template_page'] = $templatePage['uid'];
        $virtualPage['flight_route_uid'] = $flightRoute['uid'];

        return $virtualPage;
    }

    /**
     * Process placeholders in page data
     */
    public function processPlaceholdersInPageData(array $pageData, array $flightRoute): array
    {
        $placeholders = [
            '[_origin_code_]' => $flightRoute['origin_code'] ?? '',
            '[_origin_name_]' => $flightRoute['origin_name'] ?? '',
            '[_origin_type_]' => $flightRoute['origin_type'] ?? '',
            '[_destination_code_]' => $flightRoute['destination_code'] ?? '',
            '[_destination_name_]' => $flightRoute['destination_name'] ?? '',
            '[_destination_type_]' => $flightRoute['destination_type'] ?? '',
            '[_route_slug_]' => $flightRoute['route_slug'] ?? '',
            '[_route_]' => ($flightRoute['origin_name'] ?? '') . ' → ' . ($flightRoute['destination_name'] ?? ''),
            '[_route_dash_]' => ($flightRoute['origin_code'] ?? '') . '-' . ($flightRoute['destination_code'] ?? ''),
            '[_route_text_]' => ($flightRoute['origin_name'] ?? '') . ' to ' . ($flightRoute['destination_name'] ?? ''),
        ];

        foreach ($pageData as $field => $value) {
            if (is_string($value)) {
                $pageData[$field] = str_replace(array_keys($placeholders), array_values($placeholders), $value);
            }
        }

        return $pageData;
    }

    /**
     * Process placeholders in content
     */
    public function processPlaceholdersInContent(string $content, array $flightRoute): string
    {
        $placeholders = [
            '[_origin_code_]' => $flightRoute['origin_code'] ?? '',
            '[_origin_name_]' => $flightRoute['origin_name'] ?? '',
            '[_origin_type_]' => $flightRoute['origin_type'] ?? '',
            '[_destination_code_]' => $flightRoute['destination_code'] ?? '',
            '[_destination_name_]' => $flightRoute['destination_name'] ?? '',
            '[_destination_type_]' => $flightRoute['destination_type'] ?? '',
            '[_route_slug_]' => $flightRoute['route_slug'] ?? '',
            '[_route_]' => ($flightRoute['origin_name'] ?? '') . ' → ' . ($flightRoute['destination_name'] ?? ''),
            '[_route_dash_]' => ($flightRoute['origin_code'] ?? '') . '-' . ($flightRoute['destination_code'] ?? ''),
            '[_route_text_]' => ($flightRoute['origin_name'] ?? '') . ' to ' . ($flightRoute['destination_name'] ?? ''),
        ];

        return str_replace(array_keys($placeholders), array_values($placeholders), $content);
    }

    /**
     * Load template page data
     */
    public function loadTemplatePage(int $templatePageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('uid', $queryBuilder->createNamedParameter($templatePageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(200, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Find landing page by path
     */
    protected function findLandingPageByPath(string $path, Site $site): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('pages');

        $siteRootPageId = $site->getRootPageId();

        $result = $queryBuilder
            ->select('*')
            ->from('pages')
            ->where(
                $queryBuilder->expr()->eq('doktype', $queryBuilder->createNamedParameter(201, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('hidden', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', $queryBuilder->createNamedParameter(0, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('slug', $queryBuilder->createNamedParameter($path))
            )
            ->executeQuery();

        $pages = $result->fetchAllAssociative();
        
        // Filter pages that belong to this site
        foreach ($pages as $page) {
            if ($this->isPageInSite($page['uid'], $siteRootPageId)) {
                return $page;
            }
        }

        return null;
    }

    /**
     * Find flight route by route slug and landing page
     */
    protected function findFlightRoute(string $routeSlug, int $landingPageUid): ?array
    {
        $queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
            ->getQueryBuilderForTable('tx_flightlandingpages_domain_model_flightroute');

        $result = $queryBuilder
            ->select('*')
            ->from('tx_flightlandingpages_domain_model_flightroute')
            ->where(
                $queryBuilder->expr()->eq('route_slug', $queryBuilder->createNamedParameter($routeSlug)),
                $queryBuilder->expr()->eq('pid', $queryBuilder->createNamedParameter($landingPageUid, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('is_active', $queryBuilder->createNamedParameter(1, \PDO::PARAM_INT)),
                $queryBuilder->expr()->eq('deleted', 0)
            )
            ->executeQuery();

        return $result->fetchAssociative() ?: null;
    }

    /**
     * Check if page belongs to site
     */
    protected function isPageInSite(int $pageUid, int $siteRootPageId): bool
    {
        // Simple implementation - check if page is under site root
        // You might want to implement a more sophisticated check
        return true; // For now, assume all pages belong to the site
    }
}
