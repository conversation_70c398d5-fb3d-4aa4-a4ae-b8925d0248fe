<?php
namespace Bgs\FlightLandingPages\Service;

class PlaceholderService 
{
    /**
     * Replace placeholders in template with actual flight data
     * Uses [_placeholder_] syntax for safety with JS and other content
     *
     * @param string $template Template with placeholders
     * @param array $flightRouteData Complete flight route record data
     * @return string Processed template
     */
    public function replacePlaceholders(string $template, array $flightRouteData): string
    {
        if (empty($template) || empty($flightRouteData)) {
            return $template;
        }

        // Define all available placeholders from flight route data
        $placeholders = $this->buildPlaceholderMap($flightRouteData);

        // Replace all placeholders using [_placeholder_] syntax
        foreach ($placeholders as $placeholder => $value) {
            $template = str_replace('[_' . $placeholder . '_]', $value, $template);
        }



        return $template;
    }

    /**
     * Build a comprehensive map of all available placeholders
     */
    protected function buildPlaceholderMap(array $flightRouteData): array
    {
        $placeholders = [];

        // Origin placeholders
        $placeholders['origin_code'] = $flightRouteData['origin_code'] ?? '';
        $placeholders['origin_name'] = $flightRouteData['origin_name'] ?? '';
        $placeholders['origin_type'] = $flightRouteData['origin_type'] ?? '';

        // Destination placeholders
        $placeholders['destination_code'] = $flightRouteData['destination_code'] ?? '';
        $placeholders['destination_name'] = $flightRouteData['destination_name'] ?? '';
        $placeholders['destination_type'] = $flightRouteData['destination_type'] ?? '';

        // Route placeholders
        $placeholders['route_slug'] = $flightRouteData['route_slug'] ?? '';
        $placeholders['is_active'] = $flightRouteData['is_active'] ? 'active' : 'inactive';

        // Combined placeholders for convenience
        $placeholders['route'] = $placeholders['origin_code'] . ' → ' . $placeholders['destination_code'];
        $placeholders['route_dash'] = $placeholders['origin_code'] . '-' . $placeholders['destination_code'];
        $placeholders['route_text'] = $placeholders['origin_name'] . ' to ' . $placeholders['destination_name'];

        return $placeholders;
    }
}
